import { normalizeVast4 } from 'adpod-tools';
import { PrerollVmap, PrerollVmapNormalized } from '../../interfaces';

export const normalizePrerollVmap = (obj: PrerollVmap): PrerollVmapNormalized => {
  const adData = obj['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'];

  try {
    obj['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'] =
      normalizeVast4(adData);
  } catch (error) {
    console.error('Error normalizing VAST4 in preroll VMAP:', error);
    // Return the original object if normalization fails
    return obj as PrerollVmapNormalized;
  }

  return obj as PrerollVmapNormalized;
};

// /**
//  * Normalize VAST 4 response by transforming it into an object with specific structure.
//  * @param arg - VAST 4 response as a plain object.
//  * @returns Normalized VAST 4 object.
//  */
// export const normalizeVast4 = (arg: Vast4): Vast4Normalized => {
//   arg.VAST.Ad = returnAsArrayEmpty(arg.VAST.Ad).map(normalizeAdVast);

//   return arg as Vast4Normalized;
// };

// /**
//  * Normalize AdVast4 response by transforming it into an object with specific structure.
//  * @param arg - AdVast4 object.
//  * @returns Normalized AdVast4 object.
//  */
// export const normalizeAdVast = (ad: AdVast4): AdVast4Normalized => {
//   if (!ad.InLine) {
//     return ad as AdVast4Normalized;
//   }

//   ad.InLine.Creatives.Creative = returnAsArray(ad.InLine.Creatives.Creative).map(
//     normalizeCreatives
//   );

//   ad.InLine.Impression = returnAsArrayEmpty(ad.InLine.Impression);
//   ad.InLine.Error = returnAsArrayEmpty(ad.InLine.Error);

//   const extensions = ad.InLine.Extensions.Extension as Extension | Extension[] | undefined;
//   const normalizedExtensions = returnAsArrayEmpty(extensions) as any;
//   ad.InLine.Extensions.Extension = normalizedExtensions; // [extensionWBDAPM, ...returnAsArrayEmpty(otherExtensions)];

//   return ad as AdVast4Normalized;
// };

// /**
//  * Normalize AdCreative object by transforming it into an object with specific structure.
//  * @param creative - AdCreative object.
//  * @returns Normalized AdCreative object.
//  */
// export const normalizeCreatives = (creative: AdCreative): AdCreativeNormalized => {
//   if (creative.Linear) {
//     if (!creative.Linear.MediaFiles) {
//       creative.Linear.MediaFiles = { MediaFile: [] };
//     } else {
//       creative.Linear.MediaFiles.MediaFile = returnAsArrayEmpty(
//         creative.Linear.MediaFiles.MediaFile
//       );
//     }
//   }

//   return creative as AdCreativeNormalized;
// };
