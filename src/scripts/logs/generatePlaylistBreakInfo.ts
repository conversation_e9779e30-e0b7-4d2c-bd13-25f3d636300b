import { Vast4Normalized, returnAsArrayEmpty, LogLevel, AdVast4Normalized } from 'adpod-tools';
import { AdserverAd, AdserverAdFreeWheel, IPlaylistInfoItem } from '../../interfaces';
import logger from '../../libs/logging/logger';

const isAdserverAdFW = (ad: AdserverAd | undefined): ad is AdserverAdFreeWheel => {
  return !!ad && 'breakFWTrackingScripts' in ad;
};

export const generatePlaylistBreakInfo = (
  vast4Json: Vast4Normalized,
  version: string,
  channel: string,
  adServerResponseLog?: AdserverAd[]
): IPlaylistInfoItem | null => {
  if (vast4Json.VAST.Ad) {
    const allPlaylistAds: AdVast4Normalized[] = returnAsArrayEmpty(vast4Json.VAST.Ad);

    const bid = allPlaylistAds[0]?.attributesToDrop?.breakId;

    const breakDaiPlaylistAdsCount = allPlaylistAds.filter(
      (ad) => !ad.attributesToDrop?.linear
    ).length;

    const breakAllAdsCount = allPlaylistAds.length;

    const connector = adServerResponseLog?.find((a) => !!a.connector)?.connector || 'NA';

    logger(
      `STATS_BREAK_REPLACED_ADS_${breakDaiPlaylistAdsCount}_ALL_ADS_${breakAllAdsCount}_V_${version}_CONNECTOR_${connector}_CHANNEL_${channel}`,
      { breakDaiPlaylistAdsCount, breakAllAdsCount, connector, version, channel },
      LogLevel.statsBreak
    );

    return {
      bid,
      breakDaiPlaylistAdsCount,
      isWithReplacedAds: breakDaiPlaylistAdsCount > 0,
      breakAllAdsCount: allPlaylistAds.length,
      connector: adServerResponseLog ? adServerResponseLog[0]?.connector : 'N/A',
      breakAds: allPlaylistAds.map((ad) => {
        const { sequence, id } = ad._attributes;
        const { linear, campaignId, timeOffset } = ad.attributesToDrop || {};

        const universalAdId = ad.InLine?.Creatives.Creative[0].UniversalAdId?._text ?? 'N/A';

        const adServerResponseElement = adServerResponseLog?.find(
          (a) => a.breakId === bid && a.position === sequence
        );

        return {
          p: sequence,
          id,
          universalAdId,
          campaignId,
          timeOffset,
          t: linear ? 'TV' : 'DAI',
          adServer: {
            url: adServerResponseElement?.adServerUrl ?? 'N/A',
            fw_slotImpressionEvent: isAdserverAdFW(adServerResponseElement)
              ? !!adServerResponseElement.breakFWTrackingScripts?.slotImpressionUrl
              : false,
            fw_slotEndEvent: isAdserverAdFW(adServerResponseElement)
              ? !!adServerResponseElement?.breakFWTrackingScripts?.slotEndUrl
              : false
          }
        };
      })
    };
  }
  return null;
};
