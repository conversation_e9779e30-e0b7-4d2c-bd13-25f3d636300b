import { Vast4Normalized } from 'adpod-tools';

export class VastBuilder {
  private vast: Vast4Normalized = {
    VAST: {
      _attributes: {
        version: '4.0',
        'xmlns:xs': 'https://www.w3.org/2001/XMLSchema',
        xmlns: 'https://www.iab.com/VAST'
      },
      Ad: []
    }
  };

  public build() {
    return this.vast;
  }

  public addSlot(isReplaced: boolean, id?: number) {
    const index = id ?? this.vast.VAST.Ad.length + 1;
    const breakId = `Spot_${index}`;
    this.vast.VAST.Ad.push({
      _attributes: {
        id: breakId,
        sequence: index
      },
      attributesToDrop: {
        campaignId: 'CA_37178,OR_4,CR_4',
        breakId: '7515464971096321',
        linear: 'true',
        conditionalAd: false
      },
      InLine: {
        AdSystem: { _attributes: { version: '4.0' }, _text: 'TVN' },
        AdTitle: { _text: 'TVN Video Ad' },
        Creatives: {
          Creative: [
            {
              Linear: {
                Duration: { _text: '00:00:15' },
                MediaFiles: {
                  MediaFile: [
                    {
                      _attributes: {
                        width: 720,
                        type: 'video/mp4',
                        height: 404,
                        delivery: 'progressive'
                      },
                      _cdata:
                        'https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218-www.mp4'
                    }
                  ]
                },
                VideoClicks: { ClickThrough: { _attributes: { id: '' } } },
                TrackingEvents: {
                  Tracking: [
                    { _attributes: { event: 'start' } },
                    { _attributes: { event: 'start' } },
                    { _attributes: { event: 'firstQuartile' } },
                    { _attributes: { event: 'firstQuartile' } },
                    { _attributes: { event: 'midpoint' } },
                    { _attributes: { event: 'midpoint' } },
                    { _attributes: { event: 'thirdQuartile' } },
                    { _attributes: { event: 'thirdQuartile' } },
                    { _attributes: { event: 'complete' } },
                    { _attributes: { event: 'complete' } }
                  ]
                }
              }
            },
            { CompanionAds: undefined }
          ]
        },
        Impression: [
          { _attributes: { id: '' } },
          { _attributes: { id: '' } },
          { _attributes: { id: '' } }
        ],
        Error: [],
        Extensions: {
          Extension: [
            {
              _attributes: { type: 'wbdapm' },
              SlotParameters: {
                _cdata: `{ "breakID": ${breakId}, "breakType": "mirrored", "slotPosition": ${index}, "slotType": "mirrored" }`
              }
            }
          ]
        },
        Debug: { Metadata: { _text: JSON.stringify({ isReplaced }) } }
      }
    });

    return this;
  }
}
