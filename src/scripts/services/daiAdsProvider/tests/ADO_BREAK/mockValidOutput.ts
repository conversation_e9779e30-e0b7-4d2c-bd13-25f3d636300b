export default [
  {
    vast: [
      {
        _attributes: {
          id: 'Spot_2',
          sequence: 1
        },
        attributesToDrop: {
          breakId: '7515751533714321',
          campaignId: 'CA_45598,OR_1,CR_15',
          test_campaign: 'true',
          conditionalAd: false
        },
        InLine: {
          AdSystem: { _text: 'TVN' },
          AdTitle: { _text: 'TVN Video Ad' },
          Creatives: {
            Creative: [
              {
                _attributes: { id: 'r222463.mov' },
                UniversalAdId: {
                  _attributes: { idRegistry: 'TVN_Registry', idValue: 'r222463.mov' },
                  _text: 'r222463.mov'
                },
                Linear: {
                  Duration: { _text: '00:00:30' },
                  MediaFiles: {
                    MediaFile: [
                      {
                        _attributes: {
                          width: '720',
                          type: 'video/mp4',
                          height: '404',
                          delivery: 'progressive'
                        },
                        _cdata: expect.any(String)
                      }
                    ],
                    Mezzanine: {
                      _attributes: {
                        delivery: 'progressive',
                        height: 720,
                        type: 'video/mp4',
                        width: 1280
                      },
                      _cdata: expect.any(String)
                    }
                  },
                  VideoClicks: {
                    ClickThrough: {
                      _attributes: { id: '' },
                      _cdata: expect.any(String)
                    }
                  },
                  TrackingEvents: {
                    Tracking: [
                      {
                        _attributes: { event: 'start' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'firstQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'midpoint' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'thirdQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'complete' },
                        _cdata: expect.any(String)
                      }
                    ]
                  }
                }
              },
              { CompanionAds: {} }
            ]
          },
          Impression: [
            {
              _attributes: { id: '' },
              _cdata: expect.any(String)
            },
            { _attributes: { id: '' } },
            { _attributes: { id: '' } }
          ],
          Error: [],
          Extensions: {
            Extension: [
              {
                _attributes: { type: 'wbdapm' },
                SlotParameters: {
                  _cdata:
                    '{ "breakID": "7515751533714321", "breakType": "mirrored", "slotPosition": 2, "slotType": "mirrored" }'
                }
              }
            ]
          }
        }
      }
    ],
    position: 2,
    adServerUrl:
      'http://tvn.adocean.pl/ad.xml?aocodetype=1/id=A1w3VxpUJ7UcI7sa6HfkBq7OAyJJ31IAAF6sV.rSP2L.S7/ch=TVN/bid=7515751533714321/ct=linear/adid=r229291.mov_r236051.mov_r236239.mov_r235494.mov_r236278.mov_r217694.mov/mid2mindur=30/mid2dur=30/mid2maxdur=30/mid3mindur=15/mid3dur=15/mid3maxdur=15/mid5mindur=30/mid5dur=30/mid5maxdur=30/mid6mindur=30/mid6dur=30/mid6maxdur=30/mid8mindur=15/mid8dur=15/mid8maxdur=15/mid10mindur=15/mid10dur=15/mid10maxdur=15/tvn_restriction_labels=category_263641466_TELEKOMUNIKACJA-INTERNET%2Cexclude_category_263641466_TELEKOMUNIKACJA-INTERNET%2Ccategory_449732376_AFLO_MAXICORTAN%2Cexclude_category_449732376_AFLO_MAXICORTAN%2Ccategory_258675300_USLUGI_BUKMACHERSKIE%2Cexclude_category_258675300_USLUGI_BUKMACHERSKIE%2Ccategory_449287917_USP_SUPLEMENTY_DIETY%2Cexclude_category_449287917_USP_SUPLEMENTY_DIETY%2Ccategory_18_MLEKO_PROD._MLECZNE%2Ctype_HFSS%2Cexclude_category_18_MLEKO_PROD._MLECZNE/TEST_AUTO=true/v=v1_0_0_dai50_WC_AB/advid=84.38.213.227/npa=0',
    breakId: '7515751533714321',
    isReplaced: true,
    connector: 'ADOCEAN_BREAK_SCHEDULE'
  },
  {
    vast: [
      {
        _attributes: {
          id: 'Spot_3',
          sequence: 1
        },
        attributesToDrop: {
          breakId: '7515751533714321',
          campaignId: 'CA_45598,OR_1,CR_16'
        },
        InLine: {
          AdSystem: { _text: 'TVN' },
          AdTitle: { _text: 'TVN Video Ad' },
          Creatives: {
            Creative: [
              {
                _attributes: { id: 'r221261.mov' },
                UniversalAdId: {
                  _attributes: { idRegistry: 'TVN_Registry', idValue: 'r221261.mov' },
                  _text: 'r221261.mov'
                },
                Linear: {
                  Duration: { _text: '00:00:15' },
                  MediaFiles: {
                    MediaFile: [
                      {
                        _attributes: {
                          width: 720,
                          type: 'video/mp4',
                          height: 404,
                          delivery: 'progressive'
                        },
                        _cdata: expect.any(String)
                      }
                    ],
                    Mezzanine: {
                      _attributes: {
                        delivery: 'progressive',
                        height: 720,
                        type: 'video/mp4',
                        width: 1280
                      },
                      _cdata: expect.any(String)
                    }
                  },
                  VideoClicks: {
                    ClickThrough: {
                      _attributes: { id: '' },
                      _cdata: expect.any(String)
                    }
                  },
                  TrackingEvents: {
                    Tracking: [
                      {
                        _attributes: { event: 'start' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'firstQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'midpoint' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'thirdQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'complete' },
                        _cdata: expect.any(String)
                      }
                    ]
                  }
                }
              },
              { CompanionAds: {} }
            ]
          },
          Impression: [
            {
              _attributes: { id: '' },
              _cdata: expect.any(String)
            },
            { _attributes: { id: '' } },
            { _attributes: { id: '' } }
          ],
          Error: [],
          Extensions: {
            Extension: [
              {
                _attributes: { type: 'wbdapm' },
                SlotParameters: {
                  _cdata:
                    '{ "breakID": "7515751533714321", "breakType": "mirrored", "slotPosition": 3, "slotType": "mirrored" }'
                }
              }
            ]
          }
        }
      }
    ],
    position: 3,
    adServerUrl:
      'http://tvn.adocean.pl/ad.xml?aocodetype=1/id=A1w3VxpUJ7UcI7sa6HfkBq7OAyJJ31IAAF6sV.rSP2L.S7/ch=TVN/bid=7515751533714321/ct=linear/adid=r229291.mov_r236051.mov_r236239.mov_r235494.mov_r236278.mov_r217694.mov/mid2mindur=30/mid2dur=30/mid2maxdur=30/mid3mindur=15/mid3dur=15/mid3maxdur=15/mid5mindur=30/mid5dur=30/mid5maxdur=30/mid6mindur=30/mid6dur=30/mid6maxdur=30/mid8mindur=15/mid8dur=15/mid8maxdur=15/mid10mindur=15/mid10dur=15/mid10maxdur=15/tvn_restriction_labels=category_263641466_TELEKOMUNIKACJA-INTERNET%2Cexclude_category_263641466_TELEKOMUNIKACJA-INTERNET%2Ccategory_449732376_AFLO_MAXICORTAN%2Cexclude_category_449732376_AFLO_MAXICORTAN%2Ccategory_258675300_USLUGI_BUKMACHERSKIE%2Cexclude_category_258675300_USLUGI_BUKMACHERSKIE%2Ccategory_449287917_USP_SUPLEMENTY_DIETY%2Cexclude_category_449287917_USP_SUPLEMENTY_DIETY%2Ccategory_18_MLEKO_PROD._MLECZNE%2Ctype_HFSS%2Cexclude_category_18_MLEKO_PROD._MLECZNE/TEST_AUTO=true/v=v1_0_0_dai50_WC_AB/advid=84.38.213.227/npa=0',
    breakId: '7515751533714321',
    isReplaced: true,
    connector: 'ADOCEAN_BREAK_SCHEDULE'
  },
  {
    vast: [
      {
        _attributes: {
          id: 'Spot_5',
          sequence: 1
        },
        attributesToDrop: {
          breakId: '7515751533714321',
          campaignId: 'CA_45598,OR_1,CR_15'
        },
        InLine: {
          AdSystem: { _text: 'TVN' },
          AdTitle: { _text: 'TVN Video Ad' },
          Creatives: {
            Creative: [
              {
                _attributes: { id: 'r222463.mov' },
                UniversalAdId: {
                  _attributes: { idRegistry: 'TVN_Registry', idValue: 'r222463.mov' },
                  _text: 'r222463.mov'
                },
                Linear: {
                  Duration: { _text: '00:00:30' },
                  MediaFiles: {
                    MediaFile: [
                      {
                        _attributes: {
                          width: '720',
                          type: 'video/mp4',
                          height: '404',
                          delivery: 'progressive'
                        },
                        _cdata: expect.any(String)
                      }
                    ],
                    Mezzanine: {
                      _attributes: {
                        delivery: 'progressive',
                        height: 720,
                        type: 'video/mp4',
                        width: 1280
                      },
                      _cdata: expect.any(String)
                    }
                  },
                  VideoClicks: {
                    ClickThrough: {
                      _attributes: { id: '' },
                      _cdata: expect.any(String)
                    }
                  },
                  TrackingEvents: {
                    Tracking: [
                      {
                        _attributes: { event: 'start' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'firstQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'midpoint' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'thirdQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'complete' },
                        _cdata: expect.any(String)
                      }
                    ]
                  }
                }
              },
              { CompanionAds: {} }
            ]
          },
          Impression: [
            {
              _attributes: { id: '' },
              _cdata: expect.any(String)
            },
            { _attributes: { id: '' } },
            { _attributes: { id: '' } }
          ],
          Error: [],
          Extensions: {
            Extension: [
              {
                _attributes: { type: 'wbdapm' },
                SlotParameters: {
                  _cdata:
                    '{ "breakID": "7515751533714321", "breakType": "mirrored", "slotPosition": 5, "slotType": "mirrored" }'
                }
              }
            ]
          }
        }
      }
    ],
    position: 5,
    adServerUrl:
      'http://tvn.adocean.pl/ad.xml?aocodetype=1/id=A1w3VxpUJ7UcI7sa6HfkBq7OAyJJ31IAAF6sV.rSP2L.S7/ch=TVN/bid=7515751533714321/ct=linear/adid=r229291.mov_r236051.mov_r236239.mov_r235494.mov_r236278.mov_r217694.mov/mid2mindur=30/mid2dur=30/mid2maxdur=30/mid3mindur=15/mid3dur=15/mid3maxdur=15/mid5mindur=30/mid5dur=30/mid5maxdur=30/mid6mindur=30/mid6dur=30/mid6maxdur=30/mid8mindur=15/mid8dur=15/mid8maxdur=15/mid10mindur=15/mid10dur=15/mid10maxdur=15/tvn_restriction_labels=category_263641466_TELEKOMUNIKACJA-INTERNET%2Cexclude_category_263641466_TELEKOMUNIKACJA-INTERNET%2Ccategory_449732376_AFLO_MAXICORTAN%2Cexclude_category_449732376_AFLO_MAXICORTAN%2Ccategory_258675300_USLUGI_BUKMACHERSKIE%2Cexclude_category_258675300_USLUGI_BUKMACHERSKIE%2Ccategory_449287917_USP_SUPLEMENTY_DIETY%2Cexclude_category_449287917_USP_SUPLEMENTY_DIETY%2Ccategory_18_MLEKO_PROD._MLECZNE%2Ctype_HFSS%2Cexclude_category_18_MLEKO_PROD._MLECZNE/TEST_AUTO=true/v=v1_0_0_dai50_WC_AB/advid=84.38.213.227/npa=0',
    breakId: '7515751533714321',
    isReplaced: true,
    connector: 'ADOCEAN_BREAK_SCHEDULE'
  },
  {
    vast: [
      {
        _attributes: {
          id: 'Spot_6',
          sequence: 1
        },
        attributesToDrop: {
          breakId: '7515751533714321',
          campaignId: 'CA_45598,OR_1,CR_15',
          test_campaign: 'true'
        },
        InLine: {
          AdSystem: { _text: 'TVN' },
          AdTitle: { _text: 'TVN Video Ad' },
          Creatives: {
            Creative: [
              {
                _attributes: { id: 'r222463.mov' },
                UniversalAdId: {
                  _attributes: { idRegistry: 'TVN_Registry', idValue: 'r222463.mov' },
                  _text: 'r222463.mov'
                },
                Linear: {
                  Duration: { _text: '00:00:30' },
                  MediaFiles: {
                    MediaFile: [
                      {
                        _attributes: {
                          width: '720',
                          type: 'video/mp4',
                          height: '404',
                          delivery: 'progressive'
                        },
                        _cdata: expect.any(String)
                      }
                    ],
                    Mezzanine: {
                      _attributes: {
                        delivery: 'progressive',
                        height: 720,
                        type: 'video/mp4',
                        width: 1280
                      },
                      _cdata: expect.any(String)
                    }
                  },
                  VideoClicks: {
                    ClickThrough: {
                      _attributes: { id: '' },
                      _cdata: expect.any(String)
                    }
                  },
                  TrackingEvents: {
                    Tracking: [
                      {
                        _attributes: { event: 'start' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'firstQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'midpoint' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'thirdQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'complete' },
                        _cdata: expect.any(String)
                      }
                    ]
                  }
                }
              },
              { CompanionAds: {} }
            ]
          },
          Impression: [
            {
              _attributes: { id: '' },
              _cdata: expect.any(String)
            },
            { _attributes: { id: '' } },
            { _attributes: { id: '' } }
          ],
          Error: [],
          Extensions: {
            Extension: [
              {
                _attributes: { type: 'wbdapm' },
                SlotParameters: {
                  _cdata:
                    '{ "breakID": "7515751533714321", "breakType": "mirrored", "slotPosition": 6, "slotType": "mirrored" }'
                }
              }
            ]
          }
        }
      }
    ],
    position: 6,
    adServerUrl:
      'http://tvn.adocean.pl/ad.xml?aocodetype=1/id=A1w3VxpUJ7UcI7sa6HfkBq7OAyJJ31IAAF6sV.rSP2L.S7/ch=TVN/bid=7515751533714321/ct=linear/adid=r229291.mov_r236051.mov_r236239.mov_r235494.mov_r236278.mov_r217694.mov/mid2mindur=30/mid2dur=30/mid2maxdur=30/mid3mindur=15/mid3dur=15/mid3maxdur=15/mid5mindur=30/mid5dur=30/mid5maxdur=30/mid6mindur=30/mid6dur=30/mid6maxdur=30/mid8mindur=15/mid8dur=15/mid8maxdur=15/mid10mindur=15/mid10dur=15/mid10maxdur=15/tvn_restriction_labels=category_263641466_TELEKOMUNIKACJA-INTERNET%2Cexclude_category_263641466_TELEKOMUNIKACJA-INTERNET%2Ccategory_449732376_AFLO_MAXICORTAN%2Cexclude_category_449732376_AFLO_MAXICORTAN%2Ccategory_258675300_USLUGI_BUKMACHERSKIE%2Cexclude_category_258675300_USLUGI_BUKMACHERSKIE%2Ccategory_449287917_USP_SUPLEMENTY_DIETY%2Cexclude_category_449287917_USP_SUPLEMENTY_DIETY%2Ccategory_18_MLEKO_PROD._MLECZNE%2Ctype_HFSS%2Cexclude_category_18_MLEKO_PROD._MLECZNE/TEST_AUTO=true/v=v1_0_0_dai50_WC_AB/advid=84.38.213.227/npa=0',
    breakId: '7515751533714321',
    isReplaced: true,
    connector: 'ADOCEAN_BREAK_SCHEDULE'
  },
  {
    vast: [
      {
        _attributes: {
          id: 'Spot_8',
          sequence: 1
        },
        attributesToDrop: {
          breakId: '7515751533714321',
          campaignId: 'CA_45598,OR_1,CR_16',
          test_campaign: 'true'
        },
        InLine: {
          AdSystem: { _text: 'TVN' },
          AdTitle: { _text: 'TVN Video Ad' },
          Creatives: {
            Creative: [
              {
                _attributes: { id: 'r221261.mov' },
                UniversalAdId: {
                  _attributes: { idRegistry: 'TVN_Registry', idValue: 'r221261.mov' },
                  _text: 'r221261.mov'
                },
                Linear: {
                  Duration: { _text: '00:00:15' },
                  MediaFiles: {
                    MediaFile: [
                      {
                        _attributes: {
                          width: '720',
                          type: 'video/mp4',
                          height: '404',
                          delivery: 'progressive'
                        },
                        _cdata: expect.any(String)
                      }
                    ],
                    Mezzanine: {
                      _attributes: {
                        delivery: 'progressive',
                        height: 720,
                        type: 'video/mp4',
                        width: 1280
                      },
                      _cdata: expect.any(String)
                    }
                  },
                  VideoClicks: {
                    ClickThrough: {
                      _attributes: { id: '' },
                      _cdata: expect.any(String)
                    }
                  },
                  TrackingEvents: {
                    Tracking: [
                      {
                        _attributes: { event: 'start' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'firstQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'midpoint' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'thirdQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'complete' },
                        _cdata: expect.any(String)
                      }
                    ]
                  }
                }
              },
              { CompanionAds: {} }
            ]
          },
          Impression: [
            {
              _attributes: { id: '' },
              _cdata: expect.any(String)
            },
            { _attributes: { id: '' } },
            { _attributes: { id: '' } }
          ],
          Error: [],
          Extensions: {
            Extension: [
              {
                _attributes: { type: 'wbdapm' },
                SlotParameters: {
                  _cdata:
                    '{ "breakID": "7515751533714321", "breakType": "mirrored", "slotPosition": 8, "slotType": "mirrored" }'
                }
              }
            ]
          }
        }
      }
    ],
    position: 8,
    adServerUrl:
      'http://tvn.adocean.pl/ad.xml?aocodetype=1/id=A1w3VxpUJ7UcI7sa6HfkBq7OAyJJ31IAAF6sV.rSP2L.S7/ch=TVN/bid=7515751533714321/ct=linear/adid=r229291.mov_r236051.mov_r236239.mov_r235494.mov_r236278.mov_r217694.mov/mid2mindur=30/mid2dur=30/mid2maxdur=30/mid3mindur=15/mid3dur=15/mid3maxdur=15/mid5mindur=30/mid5dur=30/mid5maxdur=30/mid6mindur=30/mid6dur=30/mid6maxdur=30/mid8mindur=15/mid8dur=15/mid8maxdur=15/mid10mindur=15/mid10dur=15/mid10maxdur=15/tvn_restriction_labels=category_263641466_TELEKOMUNIKACJA-INTERNET%2Cexclude_category_263641466_TELEKOMUNIKACJA-INTERNET%2Ccategory_449732376_AFLO_MAXICORTAN%2Cexclude_category_449732376_AFLO_MAXICORTAN%2Ccategory_258675300_USLUGI_BUKMACHERSKIE%2Cexclude_category_258675300_USLUGI_BUKMACHERSKIE%2Ccategory_449287917_USP_SUPLEMENTY_DIETY%2Cexclude_category_449287917_USP_SUPLEMENTY_DIETY%2Ccategory_18_MLEKO_PROD._MLECZNE%2Ctype_HFSS%2Cexclude_category_18_MLEKO_PROD._MLECZNE/TEST_AUTO=true/v=v1_0_0_dai50_WC_AB/advid=84.38.213.227/npa=0',
    breakId: '7515751533714321',
    isReplaced: true,
    connector: 'ADOCEAN_BREAK_SCHEDULE'
  },
  {
    vast: [
      {
        _attributes: {
          id: 'Spot_10',
          sequence: 1
        },
        attributesToDrop: {
          breakId: '7515751533714321',
          campaignId: 'CA_45598,OR_1,CR_16',
          test_campaign: 'true'
        },
        InLine: {
          AdSystem: { _text: 'TVN' },
          AdTitle: { _text: 'TVN Video Ad' },
          Creatives: {
            Creative: [
              {
                _attributes: { id: 'r221261.mov' },
                UniversalAdId: {
                  _attributes: { idRegistry: 'TVN_Registry', idValue: 'r221261.mov' },
                  _text: 'r221261.mov'
                },
                Linear: {
                  Duration: { _text: '00:00:15' },
                  MediaFiles: {
                    MediaFile: [
                      {
                        _attributes: {
                          width: '720',
                          type: 'video/mp4',
                          height: '404',
                          delivery: 'progressive'
                        },
                        _cdata: expect.any(String)
                      }
                    ],
                    Mezzanine: {
                      _attributes: {
                        delivery: 'progressive',
                        height: 720,
                        type: 'video/mp4',
                        width: 1280
                      },
                      _cdata: expect.any(String)
                    }
                  },
                  VideoClicks: {
                    ClickThrough: {
                      _attributes: { id: '' },
                      _cdata: expect.any(String)
                    }
                  },
                  TrackingEvents: {
                    Tracking: [
                      {
                        _attributes: { event: 'start' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'firstQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'midpoint' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'thirdQuartile' },
                        _cdata: expect.any(String)
                      },
                      {
                        _attributes: { event: 'complete' },
                        _cdata: expect.any(String)
                      }
                    ]
                  }
                }
              },
              { CompanionAds: {} }
            ]
          },
          Impression: [
            {
              _attributes: { id: '' },
              _cdata: expect.any(String)
            },
            { _attributes: { id: '' } },
            { _attributes: { id: '' } }
          ],
          Error: [],
          Extensions: {
            Extension: [
              {
                _attributes: { type: 'wbdapm' },
                SlotParameters: {
                  _cdata:
                    '{ "breakID": "7515751533714321", "breakType": "mirrored", "slotPosition": 10, "slotType": "mirrored" }'
                }
              }
            ]
          }
        }
      }
    ],
    position: 10,
    adServerUrl:
      'http://tvn.adocean.pl/ad.xml?aocodetype=1/id=A1w3VxpUJ7UcI7sa6HfkBq7OAyJJ31IAAF6sV.rSP2L.S7/ch=TVN/bid=7515751533714321/ct=linear/adid=r229291.mov_r236051.mov_r236239.mov_r235494.mov_r236278.mov_r217694.mov/mid2mindur=30/mid2dur=30/mid2maxdur=30/mid3mindur=15/mid3dur=15/mid3maxdur=15/mid5mindur=30/mid5dur=30/mid5maxdur=30/mid6mindur=30/mid6dur=30/mid6maxdur=30/mid8mindur=15/mid8dur=15/mid8maxdur=15/mid10mindur=15/mid10dur=15/mid10maxdur=15/tvn_restriction_labels=category_263641466_TELEKOMUNIKACJA-INTERNET%2Cexclude_category_263641466_TELEKOMUNIKACJA-INTERNET%2Ccategory_449732376_AFLO_MAXICORTAN%2Cexclude_category_449732376_AFLO_MAXICORTAN%2Ccategory_258675300_USLUGI_BUKMACHERSKIE%2Cexclude_category_258675300_USLUGI_BUKMACHERSKIE%2Ccategory_449287917_USP_SUPLEMENTY_DIETY%2Cexclude_category_449287917_USP_SUPLEMENTY_DIETY%2Ccategory_18_MLEKO_PROD._MLECZNE%2Ctype_HFSS%2Cexclude_category_18_MLEKO_PROD._MLECZNE/TEST_AUTO=true/v=v1_0_0_dai50_WC_AB/advid=84.38.213.227/npa=0',
    breakId: '7515751533714321',
    isReplaced: true,
    connector: 'ADOCEAN_BREAK_SCHEDULE'
  }
];
